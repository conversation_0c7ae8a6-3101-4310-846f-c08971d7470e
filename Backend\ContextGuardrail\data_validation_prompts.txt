You are a strict data validation expert.

Your task is to validate a single data row using ONLY the structured rules below.

VALIDATION RULES (Strict JSON format):
{validation_rules}

ROW DATA:
{row_data}

IMPORTANT:
Each rule has a "type":
- If "type" = "null_check", check the listed columns for null, blank, or empty values.
- If "type" = "duplicate_check", check the `duplicate_in_dataset` flag. Fail the row if:
   - Rule requires uniqueness, AND
   - `duplicate_in_dataset` is true.
- If "type" = "value_range" and the rule mentions "Count of records", ignore the rule (since it’s dataset-level).

INSTRUCTIONS:
1. Apply ONLY the exact rules provided. Do NOT assume any extra checks.
2. If the row passes all rules:
   Respond with: {{"is_correct": true, "why": "All validations passed"}}
3. If the row fails any rule:
   Respond with: {{"is_correct": false, "why": "Specific rule failed: <short reason>"}}
4. Your explanation in `why` must be **concise**, stating exactly which rule failed.

OUTPUT:
Return strictly a single JSON object:
{{"is_correct": true/false, "why": "reason"}}

Do not include any additional text or explanation.
