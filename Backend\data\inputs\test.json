[{"procedure_name": "SP_VALIDATE_FILE_STRUCTURE", "database": "SECURE_DB", "schema": "SEC_MASTER", "table_name": "FundHoldings", "min_num_arguments": 1, "max_num_arguments": 1, "description": "Validate file structure for FundHoldings", "signature": "SP_VALIDATE_FILE_STRUCTURE(FILE_TYPE STRING)", "procedure_definition": "\nCREATE OR <PERSON><PERSON><PERSON><PERSON> PROCEDURE SECURE_DB.SEC_MASTER.SP_VALIDATE_FILE_STRUCTURE(FILE_TYPE STRING)\nRETURNS STRING\nLANGUAGE SQL\nAS\n$$\nDECLARE\n    COL_COUNT_TEMPLATE INT;\n    COL_COUNT_UPLOAD INT;\nBEGIN\n    SELECT COUNT(*) INTO COL_COUNT_TEMPLATE\n    FROM SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS\n    WHERE FILE_TYPE = FILE_TYPE;\n\n    SELECT COUNT(*) INTO COL_COUNT_UPLOAD\n    FROM INFORMATION_SCHEMA.COLUMNS\n    WHERE TABLE_SCHEMA = 'SEC_MASTER'\n      AND TABLE_NAME = 'FundHoldings';\n\n    IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN\n        RETURN 'File structure mismatch. Expected ' || COL_COUNT_TEMPLATE || ' columns, found ' || COL_COUNT_UPLOAD;\n    END IF;\n\n    RETURN 'File structure valid';\nEND;\n$$;\n"}, {"procedure_name": "SP_VALIDATE_MANDATORY_COLUMNS", "database": "SECURE_DB", "schema": "SEC_MASTER", "table_name": "FundHoldings", "min_num_arguments": 1, "max_num_arguments": 1, "description": "Validate mandatory columns for FundHoldings", "signature": "SP_VALIDATE_MANDATORY_COLUMNS(FILE_TYPE STRING)", "procedure_definition": "\nCREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE SECURE_DB.SEC_MASTER.SP_VALIDATE_MANDATORY_COLUMNS(FILE_TYPE STRING)\nRETURNS STRING\nLANGUAGE SQL\nAS\n$$\nDECLARE\n    MISSING_COUNT INT;\nB<PERSON>IN\n    SELECT COUNT(*) INTO MISSING_COUNT\n    FROM SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS c\n    LEFT JOIN INFORMATION_SCHEMA.COLUMNS u\n    ON c.COLUMN_NAME = u.COLUMN_NAME\n       AND u.TABLE_SCHEMA = 'SEC_MASTER'\n       AND u.TABLE_NAME = 'FundHoldings'\n    WHERE c.FILE_TYPE = FILE_TYPE\n      AND c.IS_REQUIRED = TRUE\n      AND u.COLUMN_NAME IS NULL;\n\n    IF MISSING_COUNT > 0 THEN\n        RETURN 'Missing required columns.';\n    END IF;\n\n    RETURN 'All required columns present.';\nEND;\n$$;\n"}, {"procedure_name": "SP_CLEAN_DATA", "database": "SECURE_DB", "schema": "SEC_MASTER", "table_name": "FundHoldings", "min_num_arguments": 0, "max_num_arguments": 0, "description": "Clean FundHoldings data", "signature": "SP_CLEAN_DATA()", "procedure_definition": "\nCREATE OR <PERSON><PERSON><PERSON><PERSON> PROCEDURE SECURE_DB.SEC_MASTER.SP_CLEAN_DATA()\nRETURNS STRING\nLANGUAGE SQL\nAS\n$$\nBEGIN\n    DELETE FROM SECURE_DB.SEC_MASTER.FundHoldings\n    WHERE (NVL(NAV,0) = 0)\n      AND (NVL(OWNERSHIP_PERCENTAGE,0) = 0)\n      AND (NVL(CAPITAL_CALLED,0) = 0)\n      AND (NVL(NO_OF_SHARES,0) = 0)\n      AND (NVL(COMMITTED_CAPITAL,0) = 0);\n\n    DELETE FROM SECURE_DB.SEC_MASTER.FundHoldings\n    WHERE UPPER(UNIQUE_ID) LIKE 'TOTAL%';\n\n    UPDATE SECURE_DB.SEC_MASTER.FundHoldings\n    SET UNIQUE_ID = REGEXP_REPLACE(UNIQUE_ID, '[^A-Za-z0-9]', '', 'g');\n\n    RETURN 'Data cleaned successfully.';\nEND;\n$$;\n"}, {"procedure_name": "SP_FINAL_VALIDATION", "database": "SECURE_DB", "schema": "SEC_MASTER", "table_name": "FundHoldings", "min_num_arguments": 0, "max_num_arguments": 0, "description": "Final validation for FundHoldings data", "signature": "SP_FINAL_VALIDATION()", "procedure_definition": "\nCREATE OR <PERSON><PERSON><PERSON><PERSON> PROCEDURE SECURE_DB.SEC_MASTER.SP_FINAL_VALIDATION()\nRETURNS STRING\nLANGUAGE SQL\nAS\n$$\nDECLARE\n    REC_COUNT INT;\n    DUP_COUNT INT;\n    NULL_UID_COUNT INT;\nBEGIN\n    SELECT COUNT(*) INTO REC_COUNT\n    FROM SECURE_DB.SEC_MASTER.FundHoldings;\n    IF REC_COUNT = 0 THEN\n        RETURN 'Validation failed: File is empty.';\n    END IF;\n\n    SELECT COUNT(*) INTO NULL_UID_COUNT\n    FROM SECURE_DB.SEC_MASTER.FundHoldings\n    WHERE UNIQUE_ID IS NULL OR TRIM(UNIQUE_ID) = '';\n\n    IF NULL_UID_COUNT > 0 THEN\n        RETURN 'Validation failed: Unique Identifier missing.';\n    END IF;\n\n    SELECT COUNT(*) INTO DUP_COUNT\n    FROM (\n        SELECT UNIQUE_ID, COUNT(*)\n        FROM SECURE_DB.SEC_MASTER.FundHoldings\n        GROUP BY UNIQUE_ID\n        HAVING COUNT(*) > 1\n    );\n\n    IF DUP_COUNT > 0 THEN\n        RETURN 'Validation failed: Duplicate Unique Identifiers.';\n    END IF;\n\n    RETURN 'Final validation passed.';\nEND;\n$$;\n"}]