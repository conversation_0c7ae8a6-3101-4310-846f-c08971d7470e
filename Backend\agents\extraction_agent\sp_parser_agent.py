import os
import json
import pandas as pd
from config.llm_config import get_llm
from config.custom_log import get_logger


class SPExtractorAgent:
    def __init__(self, prompt_path="ContextGuardrail/sp_parser_prompts.txt"):
        self.llm = get_llm()
        self.logger = get_logger("SPExtractorAgent", "logs/sp_parser_agent.log")
        self.prompt_template = self._load_prompt_template(prompt_path)
        self.logger.info("SPExtractorAgent initialized.")

    def _load_prompt_template(self, path):
        with open(path, "r", encoding="utf-8") as f:
            return f.read()

    def _load_metadata_columns(self, metadata_columns_path):
        self.logger.info(f"Loading metadata columns from Excel: {metadata_columns_path}")
        df = pd.read_excel(metadata_columns_path)
        metadata_columns = df.to_dict(orient="records")
        self.logger.info(f"Loaded {len(metadata_columns)} metadata columns.")
        return metadata_columns

    def _build_prompt(self, procedure_text, procedure_name, metadata_columns):
        required_columns = [col["FILE_COLUMN_NAME"].strip() for col in metadata_columns]
        data_types = {col["FILE_COLUMN_NAME"].strip(): col["Data_Type"] for col in metadata_columns}

        self.logger.info(f"Building prompt for procedure: {procedure_name}")
        return self.prompt_template.format(
            required_columns=json.dumps(required_columns, indent=2),
            data_types=json.dumps(data_types, indent=2),
            procedure_text=procedure_text,
            procedure_name=procedure_name
        )

    def run(self, stored_procedure_file_path, metadata_columns_path):
        try:
            self.logger.info(f"Running SP extraction for file: {stored_procedure_file_path}")

            with open(stored_procedure_file_path, "r", encoding="utf-8") as f:
                sp_data_list = json.load(f)

            if not isinstance(sp_data_list, list):
                sp_data_list = [sp_data_list]

            self.logger.info(f"Loaded {len(sp_data_list)} stored procedures for extraction.")

            metadata_columns = self._load_metadata_columns(metadata_columns_path)

            results = []

            for sp_data in sp_data_list:
                procedure_text = sp_data.get("procedure_definition", "")
                procedure_name = sp_data.get("procedure_name", "UnnamedProcedure")

                prompt = self._build_prompt(procedure_text, procedure_name, metadata_columns)
                self.logger.info(f"Prompt built for procedure {procedure_name}. Sending to LLM...")

                response = self.llm(prompt)  
                self.logger.info(f"LLM response received for {procedure_name}.")

                parsed = json.loads(response)
                self.logger.info(f"LLM response parsed successfully for {procedure_name}.")

                schema_path = f"data\outputs\schema_validation.json"
                data_json_path = f"data\outputs\schema_validation.json"

                os.makedirs("outputs", exist_ok=True)

                with open(schema_path, "w", encoding="utf-8") as f:
                    json.dump({"schema_definition": parsed.get("schema_validation", [])}, f, indent=2)


                with open(data_json_path, "w", encoding="utf-8") as f:
                    json.dump(parsed.get("data_validation", []), f, indent=2)

                results.append({
                    "procedure_name": procedure_name,
                    "schema_path": schema_path,
                    "data_json": data_json_path
                })

            self.logger.info("SP Extraction completed for all procedures.")

            return {
                "success": True,
                "schema_path": schema_path,
                "data_json": data_json_path,
                "results": results,
                "message": "SP Extraction completed successfully for all procedures."
            }

        except Exception as e:
            error_message = f"SP Extraction failed: {e}"
            self.logger.error(error_message)
            return {
                "success": False,
                "message": error_message
            }
