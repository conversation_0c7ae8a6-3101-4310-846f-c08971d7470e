from agents.extraction_agent.rule_base_agent import RuleExtractionAgent
from agents.file_validation_agent import FileValidationAgent
from agents.extraction_agent.sp_parser_agent import SPExtractorAgent
from agents.data_validation_agent import DataValidatorAgent
from agents.guardrail import GuardRailAgent
from config.llm_config import get_llm
from config.custom_log import get_logger


class MasterValidationAgent:
    def __init__(self):
        self.llm = get_llm()
        self.logger = get_logger("MasterValidationAgent", "logs/master_agent.log")

        self.rule_agent = RuleExtractionAgent()
        self.file_agent = FileValidationAgent()
        self.sp_agent = SPExtractorAgent()
        self.data_validator = DataValidatorAgent()
        self.guardrail_agent = GuardRailAgent()

    def log(self, message: str):
        self.logger.info(message)

    def guardrail_check(self, result: dict) -> bool:
        guard_result = self.guardrail_agent.check(result.get("message", ""))
        if not guard_result.get("valid", True):
            self.log(f"[GUARDRAIL] Blocked: {guard_result['message']}")
            return False
        return True

    def run(self, metadata_excel_path: str, user_excel_path: str, stored_procedure_file_path: str) -> dict:
        try:
            self.log("[MASTER] Starting Multi-Agent Orchestration")

            rule_result = self.rule_agent.run(metadata_excel_path)
            if not rule_result.get("success"):
                return {"success": False, "message": "Failed at Rule Extraction."}

            file_result = self.file_agent.run(user_excel_path, rule_result.get("schema_path"))
            if not file_result.get("success"):
                return {"success": False, "message": "Failed at File Validation."}

            sp_result = self.sp_agent.run(stored_procedure_file_path, metadata_excel_path)
            if not sp_result or not sp_result.get("success"):
                return {"success": False, "message": "Failed at Stored Procedure Extraction."}

            sp_results_list = sp_result.get("results", [])
            if sp_results_list:
                data_validation_json_path = sp_results_list[0].get("data_json")
                if data_validation_json_path:
                    self.data_validator.load_rules(data_validation_json_path)
                else:
                    return {"success": False, "message": "SP Extraction returned no validation rules."}
            else:
                return {"success": False, "message": "SP Extraction returned empty results."}

            self.data_validator.load_prompt("ContextGuardrail\data_validation_prompts.txt")
            data_result = self.data_validator.run(user_excel_path)
            if not data_result.get("success"):
                return {"success": False, "message": "Failed at Data Validation."}

            self.log("[MASTER] Multi-Agent Validation Flow Completed Successfully.")
            return {"success": True, "message": "All stages completed successfully."}

        except Exception as e:
            self.log(f"[MASTER] Exception occurred: {str(e)}")
            return {"success": False, "message": f"Pipeline crashed with error: {str(e)}"}
