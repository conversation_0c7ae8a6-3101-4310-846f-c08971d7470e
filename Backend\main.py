from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import openpyxl
import os
import glob
import json
from datetime import datetime

app = FastAPI(title="File Agent API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Path to the folder containing Excel files
EXCEL_FOLDER_PATH = r"D:\64 sqaure\CBRE_Validation_Agent_UI\Backend\outputs"

# Cache for file monitoring
last_file_check = {
    "file_path": None,
    "file_name": None,
    "modified_time": None,
    "last_check_time": None,
    "json_modified_time": None
}

def get_file_info(file_path):
    """Get file modification time and name"""
    if not os.path.exists(file_path):
        return None, None

    modified_time = os.path.getmtime(file_path)
    file_name = os.path.basename(file_path)
    return modified_time, file_name

def find_excel_file(folder_path: str) -> tuple:
    """Find the latest Excel file in the specified folder and return file info"""
    try:
        print(f"Looking for Excel files in: {folder_path}")
        
        # Look for Excel files with common extensions
        excel_patterns = [
            os.path.join(folder_path, "*.xlsx"),
            os.path.join(folder_path, "*.xls"),
            os.path.join(folder_path, "*.xlsm")
        ]
        
        excel_files = []
        for pattern in excel_patterns:
            excel_files.extend(glob.glob(pattern))
        
        if not excel_files:
            print("No Excel files found in the folder")
            return None, None, None
        
        # Sort files by modification time (newest first)
        excel_files.sort(key=os.path.getmtime, reverse=True)
        latest_file = excel_files[0]
        
        modified_time, file_name = get_file_info(latest_file)
        
        print(f"Found Excel file: {latest_file}")
        print(f"File modified time: {datetime.fromtimestamp(modified_time)}")
        
        return latest_file, file_name, modified_time
        
    except Exception as e:
        print(f"Error finding Excel file: {str(e)}")
        return None, None, None

def read_initial_check_results(folder_path: str) -> tuple:
    """Read initial check results from JSON file"""
    try:
        json_file_path = os.path.join(folder_path, "initial_check_results.json")
        
        if not os.path.exists(json_file_path):
            print(f"Initial check results file not found: {json_file_path}")
            # Return default values when file doesn't exist
            return None, "Initial check results file not found yet", None
        
        json_modified_time = os.path.getmtime(json_file_path)
        
        with open(json_file_path, 'r') as f:
            check_results = json.load(f)
        
        print(f"Initial check results loaded: {check_results}")
        
        # Check if the results indicate the data is correct
        if isinstance(check_results, list) and len(check_results) > 0:
            first_result = check_results[0]
            is_correct = first_result.get("is_correct", False)
            reason = first_result.get("why", "No reason provided")
            
            return is_correct, reason, json_modified_time
        
        return False, "Invalid check results format", json_modified_time
        
    except json.JSONDecodeError:
        print("Error decoding JSON file - file might be incomplete")
        return False, "Invalid JSON format in check results", None
    except Exception as e:
        print(f"Error reading initial check results: {str(e)}")
        return False, f"Error reading check results: {str(e)}", None
def read_excel_file(file_path: str) -> list:
    """Read Excel file and convert to list of dictionaries"""
    try:
        print(f"Attempting to read file: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            raise FileNotFoundError(f"Excel file not found: {file_path}")
        
        print("File found, loading workbook...")
        workbook = openpyxl.load_workbook(file_path)
        sheet = workbook.active
        print(f"Workbook loaded, active sheet: {sheet.title}")
        
        # Get headers from first row
        headers = []
        for cell in sheet[1]:
            headers.append(cell.value if cell.value else "")
        print(f"Headers found: {headers}")
        
        # Read data rows
        data = []
        for row in sheet.iter_rows(min_row=2, values_only=True):
            row_data = {}
            for i, value in enumerate(row):
                if i < len(headers):
                    if value is None:
                        row_data[headers[i]] = ""
                    else:
                        row_data[headers[i]] = str(value)
            data.append(row_data)
        
        workbook.close()
        print(f"Successfully read {len(data)} rows")
        return data
        
    except Exception as e:
        print(f"Error reading Excel file: {str(e)}")
        raise e

@app.get("/")
async def root():
    return {"message": "File Agent API is running"}

@app.get("/api/initial-check")
async def get_initial_check():
    """Get initial check results"""
    try:
        print("Initial check endpoint called")
        
        # Read initial check results
        is_correct, reason, json_modified_time = read_initial_check_results(EXCEL_FOLDER_PATH)
        
        if is_correct is None:
            raise HTTPException(status_code=404, detail="Initial check results file not found")
        
        return JSONResponse(content={
            "is_correct": is_correct,
            "reason": reason,
            "check_time": datetime.fromtimestamp(json_modified_time).isoformat() if json_modified_time else None
        })
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in initial check: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error reading initial check results: {str(e)}")

@app.get("/api/excel-data")
async def get_excel_data():
    """Get raw data from Excel file - only if initial check passes"""
    try:
        print("Excel data endpoint called")
        
        # First, check the initial validation results
        is_correct, reason, json_modified_time = read_initial_check_results(EXCEL_FOLDER_PATH)
        
        if is_correct is None:
            raise HTTPException(status_code=404, detail="Initial check results file not found")
        
        if not is_correct:
            raise HTTPException(status_code=400, detail=f"Data validation failed: {reason}")
        
        # Find the latest Excel file in the folder
        excel_file_path, file_name, modified_time = find_excel_file(EXCEL_FOLDER_PATH)
        
        if not excel_file_path:
            print("No Excel file found in the folder")
            # Reset cache when no file found
            last_file_check["file_path"] = None
            last_file_check["file_name"] = None
            last_file_check["modified_time"] = None
            last_file_check["json_modified_time"] = None
            raise HTTPException(status_code=404, detail="No Excel file found in the downloads folder")
        
        # Check if this is a new file or file has been modified
        is_new_file = (
            last_file_check["file_path"] != excel_file_path or
            last_file_check["modified_time"] != modified_time or
            last_file_check["json_modified_time"] != json_modified_time
        )
        
        # Update cache
        last_file_check["file_path"] = excel_file_path
        last_file_check["file_name"] = file_name
        last_file_check["modified_time"] = modified_time
        last_file_check["json_modified_time"] = json_modified_time
        last_file_check["last_check_time"] = datetime.now().timestamp()
        
        # Read the Excel file
        excel_data = read_excel_file(excel_file_path)
        
        if not excel_data:
            print("No data found in Excel file")
            raise HTTPException(status_code=404, detail="No data found in Excel file")
        
        print(f"Returning {len(excel_data)} rows of data")
        return JSONResponse(content={
            "data": excel_data,
            "file_name": file_name,
            "is_new_file": is_new_file,
            "file_modified_time": datetime.fromtimestamp(modified_time).isoformat(),
            "validation_passed": True,
            "validation_reason": reason
        })
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"General error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error reading Excel file: {str(e)}")

@app.get("/api/check-file")
async def check_file():
    """Check if there's a new file and if validation passes"""
    try:
        # First check validation results
        is_correct, reason, json_modified_time = read_initial_check_results(EXCEL_FOLDER_PATH)
        
        if is_correct is None:
            return JSONResponse(content={
                "file_found": False,
                "file_name": None,
                "is_new_file": False,
                "validation_passed": False,
                "validation_reason": "Initial check results file not found"
            })
        
        if not is_correct:
            return JSONResponse(content={
                "file_found": False,
                "file_name": None,
                "is_new_file": False,
                "validation_passed": False,
                "validation_reason": reason
            })
        
        # Then check for Excel file
        excel_file_path, file_name, modified_time = find_excel_file(EXCEL_FOLDER_PATH)
        
        if not excel_file_path:
            return JSONResponse(content={
                "file_found": False,
                "file_name": None,
                "is_new_file": False,
                "validation_passed": True,
                "validation_reason": reason
            })
        
        # Check if this is a new file or file has been modified
        is_new_file = (
            last_file_check["file_path"] != excel_file_path or
            last_file_check["modified_time"] != modified_time or
            last_file_check["json_modified_time"] != json_modified_time
        )
        
        return JSONResponse(content={
            "file_found": True,
            "file_name": file_name,
            "is_new_file": is_new_file,
            "file_modified_time": datetime.fromtimestamp(modified_time).isoformat(),
            "validation_passed": True,
            "validation_reason": reason
        })
        
    except Exception as e:
        print(f"Error checking file: {str(e)}")
        return JSONResponse(content={
            "file_found": False,
            "file_name": None,
            "is_new_file": False,
            "validation_passed": False,
            "validation_reason": f"Error: {str(e)}"
        })

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)