import React, { useState, useEffect } from "react";
import {
  FileText,
  Download,
  Eye,
  Search,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  FolderOpen,
  Clock,
  BookCopy,
  ShieldX
} from "lucide-react";

const ExcelDataViewer = () => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [processingStatus, setProcessingStatus] = useState("idle");
  const [searchTerm, setSearchTerm] = useState("");
  const [showDataView, setShowDataView] = useState(false);
  const [fileName, setFileName] = useState("");
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [toast, setToast] = useState({ show: false, message: "", type: "" });
  const [fileCheckInterval, setFileCheckInterval] = useState(null);
  const [validationStatus, setValidationStatus] = useState(null);
  const [validationError, setValidationError] = useState("");
console.log("Data:", validationStatus);
  const columns =
    data.length > 0
      ? Object.keys(data[0]).filter((col) => col !== "reason")
      : [];

  // Toast functions
  const showToast = (message, type = "info") => {
    setToast({ show: true, message, type });
    setTimeout(() => {
      setToast({ show: false, message: "", type: "" });
    }, 5000);
  };
// Remove the second useEffect for initial checks (keep only this one)
useEffect(() => {
  const checkInitialValidation = async () => {
    try {
      const response = await fetch("http://localhost:8000/api/initial-check");
      if (response.ok) {
        const result = await response.json();
        setValidationStatus(result.is_correct);
        if (!result.is_correct) {
          setValidationError(result.reason || "Validation failed");
          setProcessingStatus("validation_failed"); // Add this line
        }
      }
    } catch (error) {
      console.error("Error checking initial validation:", error);
      setValidationStatus(false);
      setValidationError("Error checking validation status");
      setProcessingStatus("validation_failed"); // Add this line
    }
  };

  const intervalId = setInterval(checkInitialValidation, 5000);
  checkInitialValidation(); // Run immediately on mount

  return () => clearInterval(intervalId);
}, []);

// Modify the fetchExcelData function to properly handle validation
const fetchExcelData = async () => {
  setProcessingStatus("processing");
  try {
    // First check initial validation
    const response = await fetch("http://localhost:8000/api/initial-check");
    if (!response.ok) throw new Error("Validation check failed");
    
    const validationResult = await response.json();
    if (!validationResult.is_correct) {
      setValidationStatus(false);
      setValidationError(validationResult.reason || "Validation failed");
      setProcessingStatus("validation_failed");
      return;
    }

    // Rest of your fetch logic...
    const dataResponse = await fetch("http://localhost:8000/api/excel-data");
    if (!dataResponse.ok) {
      if (dataResponse.status === 404) {
        setProcessingStatus("no_file");
        if (!isMonitoring) startFileMonitoring();
        return;
      }
      throw new Error("Failed to fetch data");
    }

    const result = await dataResponse.json();
    setData(result.data);
    setFileName(result.file_name || "Unknown file");
    setProcessingStatus("completed");

    if (isMonitoring) stopFileMonitoring();
    if (result.is_new_file) {
      showToast(`File loaded: ${result.file_name}`, "success");
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    setProcessingStatus("error");
    if (isMonitoring) stopFileMonitoring();
  }
};
  // Check initial validation
  useEffect(() => {
  const intervalId = setInterval(async () => {
    try {
      const response = await fetch("http://localhost:8000/api/initial-check");
      if (response.ok) {
        const result = await response.json();

        setValidationStatus(result.is_correct);
        if (!result.is_correct) {
          setValidationError(result.reason || "Validation failed");
        }
      }
    } catch (error) {
      console.error("Error checking initial validation:", error);
      setValidationStatus(false);
      setValidationError("Error checking validation status");
    }
  }, 5000); // checks every 5 seconds

  return () => clearInterval(intervalId); // cleanup on unmount
}, []);

  // Check for new files
  const checkForNewFile = async () => {
    try {
      const isValid = await checkInitialValidation();
      if (!isValid) return false;

      const response = await fetch("http://localhost:8000/api/check-file");
      if (response.ok) {
        const result = await response.json();

        if (result.file_found && result.is_new_file) {
          showToast(`New file detected: ${result.file_name}`, "success");
          // Auto-load the new file
          await fetchExcelData();
          return true;
        }
      }
    } catch (error) {
      console.error("Error checking for new file:", error);
    }
    return false;
  };

  // Start monitoring for new files
  const startFileMonitoring = () => {
    if (fileCheckInterval) {
      clearInterval(fileCheckInterval);
    }

    setIsMonitoring(true);
    const interval = setInterval(checkForNewFile, 3000); // Check every 3 seconds
    setFileCheckInterval(interval);

    showToast("Processing on new files...", "info");
  };

  // Stop monitoring for new files
  const stopFileMonitoring = () => {
    if (fileCheckInterval) {
      clearInterval(fileCheckInterval);
      setFileCheckInterval(null);
    }
    setIsMonitoring(false);
    showToast("Processing on new files", "info");
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (fileCheckInterval) {
        clearInterval(fileCheckInterval);
      }
    };
  }, [fileCheckInterval]);

  // Fetch data from backend
  // const fetchExcelData = async () => {
  //   setProcessingStatus("processing");
  //   try {
  //     // First check initial validation
  //     const isValid = await checkInitialValidation();
  //     if (!isValid) {
  //       setProcessingStatus("validation_failed");
  //       return;
  //     }

  //     const response = await fetch("http://localhost:8000/api/excel-data");

  //     if (!response.ok) {
  //       if (response.status === 404) {
  //         setProcessingStatus("no_file");
  //         // Start monitoring when no file is found
  //         if (!isMonitoring) {
  //           startFileMonitoring();
  //         }
  //         return;
  //       }
  //       throw new Error("Failed to fetch data");
  //     }

  //     const result = await response.json();
  //     setData(result.data);
  //     setFileName(result.file_name || "Unknown file");
  //     setProcessingStatus("completed");

  //     // Stop monitoring when file is successfully loaded
  //     if (isMonitoring) {
  //       stopFileMonitoring();
  //     }

  //     // Show toast for new file
  //     if (result.is_new_file) {
  //       showToast(`File loaded: ${result.file_name}`, "success");
  //     }
  //   } catch (error) {
  //     console.error("Error fetching data:", error);
  //     setProcessingStatus("error");

  //     // Stop monitoring on error
  //     if (isMonitoring) {
  //       stopFileMonitoring();
  //     }
  //   }
  // };

  // Load data on component mount
  useEffect(() => {
    fetchExcelData();
  }, []);

  // Search functionality
  const applySearch = (data) => {
    if (!searchTerm) return data;

    return data.filter((row) =>
      Object.values(row).some(
        (value) =>
          value &&
          value.toString().toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  };

  useEffect(() => {
    if (data.length > 0) {
      setFilteredData(applySearch(data));
    }
  }, [data, searchTerm]);

  const downloadExcelFile = () => {
    if (data.length === 0) return;

    // Create CSV content
    const headers = Object.keys(data[0]).join(",");
    const csvContent = data
      .map((row) =>
        Object.values(row)
          .map((value) => `"${String(value).replace(/"/g, '""')}"`)
          .join(",")
      )
      .join("\n");

    const fullCsv = headers + "\n" + csvContent;

    // Create and download file
    const blob = new Blob([fullCsv], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "fund_holdings_data.csv";
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getColumnIcon = (columnName) => {
    const iconClass = "w-2 h-2 rounded-full";
    switch (columnName.toLowerCase()) {
      case "portfolio_id":
        return <div className={`${iconClass} bg-blue-500`}></div>;
      case "fund_name":
        return <div className={`${iconClass} bg-purple-500`}></div>;
      case "nav":
      case "capital_called":
      case "committed_capital":
        return <div className={`${iconClass} bg-green-500`}></div>;
      case "registered_holder":
        return <div className={`${iconClass} bg-orange-500`}></div>;
      case "no_of_shares":
      case "ownership_percentage":
        return <div className={`${iconClass} bg-pink-500`}></div>;
      case "period":
        return <div className={`${iconClass} bg-indigo-500`}></div>;
      case "unique_id":
        return <div className={`${iconClass} bg-cyan-500`}></div>;
      default:
        return <div className={`${iconClass} bg-gray-500`}></div>;
    }
  };

  const getStatusBadge = (row) => {
    if (row.is_correct === "True") {
      return (
        <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          Valid
        </div>
      );
    } else {
      return (
        <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <AlertCircle className="w-3 h-3 mr-1" />
          Invalid
        </div>
      );
    }
  };


const wait = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

const waitForValidValidation = async () => {
  while (true) {
    const isValid = await checkInitialValidation();
    if (isValid) return true;
    await wait(3000); // wait 3 seconds before retrying
  }
};










  const getDuplicateStatus = (row) => {
    if (row.duplicate_in_dataset === "True") {
      return (
        <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          <AlertCircle className="w-3 h-3 mr-1" />
          Duplicate
        </div>
      );
    } else {
      return (
        <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          Unique
        </div>
      );
    }
  };

  const getSummaryStats = () => {
    const totalRows = data.length;
    const validRows = data.filter((row) => row.is_correct === "True").length;
    const invalidRows = totalRows - validRows;
    const duplicateRows = data.filter(
      (row) => row.duplicate_in_dataset === "True"
    ).length;

    return { totalRows, validRows, invalidRows, duplicateRows };
  };

  const stats = getSummaryStats();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Toast Notification */}
      {toast.show && (
        <div
          className={`fixed top-4 right-4 z-50 px-6 py-4 rounded-lg shadow-lg transition-all duration-300 ${
            toast.type === "success"
              ? "bg-green-500 text-white"
              : toast.type === "error"
              ? "bg-red-500 text-white"
              : "bg-blue-500 text-white"
          }`}
        >
          <div className="flex items-center">
            {toast.type === "success" && (
              <CheckCircle className="w-5 h-5 mr-2" />
            )}
            {toast.type === "error" && <AlertCircle className="w-5 h-5 mr-2" />}
            {toast.type === "info" && <Clock className="w-5 h-5 mr-2" />}
            <span>{toast.message}</span>
          </div>
        </div>
      )}

      <div className="max-w-7x2 mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            CBRE File Agent
          </h1>
          
        </div>

        {/* Refresh Section */}
        <div className="bg-white rounded-2xl shadow-md p-8 mb-8 border border-gray-200 w-1/2 ml-auto mr-auto">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">
                Data Analysis Results
              </h2>
              {fileName && (
                <p className="text-md text-gray-600 mt-1">
                  Current file: <span className="font-medium">{fileName}</span>
                </p>
              )}
              {isMonitoring && (
                <p className="text-sm text-blue-600 mt-1 flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                  Processing on file...
                </p>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={fetchExcelData}
                disabled={processingStatus === "processing"}
                className="flex items-center bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-md transform hover:scale-105 disabled:opacity-50"
              >
                <RefreshCw
                  className={`h-5 w-5 mr-2 ${
                    processingStatus === "processing" ? "animate-spin" : ""
                  }`}
                />
                <span className="font-medium">
                  {processingStatus === "processing"
                    ? "Loading..."
                    : "Refresh Data"}
                </span>
              </button>

              {processingStatus === "no_file" && (
                <button
                  onClick={
                    isMonitoring ? stopFileMonitoring : startFileMonitoring
                  }
                  className={`flex items-center px-6 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl transform hover:scale-105 ${
                    isMonitoring
                      ? "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white"
                      : "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white"
                  }`}
                >
                  <Clock className="h-5 w-5 mr-2" />
                  <span className="font-medium">
                    {isMonitoring ? "Stop Processing" : "Start Processing"}
                  </span>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Processing Status */}
        {processingStatus === "processing" && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
              <span className="text-lg font-medium text-blue-800">
                Loading and analyzing data...
              </span>
            </div>
          </div>
        )}

        {/* Validation Failed Status */}
        {processingStatus === "validation_failed" && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-8 mb-8">
            <div className="flex flex-col items-center justify-center text-center">
              <AlertCircle className="h-16 w-16 text-red-600 mb-4" />
              <h3 className="text-xl font-semibold text-red-800 mb-2">
                {validationError === "Waiting for validation results..."
                  ? "Waiting for Validation Results"
                  : "Data Validation Failed"}
              </h3>
              <p className="text-gray-700 mb-6 max-w-lg">
                {validationError ||
                  "The data did not pass initial validation checks."}
              </p>
              <button
                onClick={fetchExcelData}
                className="flex items-center bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl transform hover:scale-105"
              >
                <RefreshCw className="h-5 w-5 mr-2" />
                <span className="font-medium">Check Again</span>
              </button>
            </div>
          </div>
        )}

        {/* No File Status */}
        {processingStatus === "no_file" && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-8 mb-8">
            <div className="flex flex-col items-center justify-center text-center">
              <FolderOpen className="h-16 w-16 text-yellow-600 mb-4" />
              <h3 className="text-xl font-semibold text-yellow-800 mb-2">
                No Excel File Found
              </h3>
              {isMonitoring ? (
                <div className="flex flex-col items-center space-y-4">
                  <div className="flex items-center text-blue-600">
                    <div className="w-4 h-4 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                    <span className="font-medium">Processing on file...</span>
                  </div>
                </div>
              ) : (
                <div className="flex space-x-4">
                  <button
                    onClick={fetchExcelData}
                    className="flex items-center bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-md transform hover:scale-105"
                  >
                    <RefreshCw className="h-5 w-5 mr-2" />
                    <span className="font-medium">Check Again</span>
                  </button>
                  <button
                    onClick={startFileMonitoring}
                    className="flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-md transform hover:scale-105"
                  >
                    <Clock className="h-5 w-5 mr-2" />
                    <span className="font-medium">Start Auto-Monitor</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Error Status */}
        {processingStatus === "error" && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <div className="flex items-center justify-center">
              <AlertCircle className="h-8 w-8 text-red-600 mr-4" />
              <span className="text-lg font-medium text-red-800">
                Error loading data. Please try again.
              </span>
            </div>
          </div>
        )}

        {/* Data Summary - Only show if validation passed */}
        {processingStatus === "completed" && validationStatus !== false && (
          <>
            <div className="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                Data Summary
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-3xl font-bold">{stats.totalRows}</p>
                      <p className="text-blue-100 font-medium">Total Records</p>
                    </div>
                    <FileText className="h-8 w-8 text-blue-200" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-3xl font-bold">{stats.validRows}</p>
                      <p className="text-green-100 font-medium">
                        Valid Records
                      </p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-200" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-xl p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-3xl font-bold">{stats.invalidRows}</p>
                      <p className="text-red-100 font-medium">
                        Invalid Records
                      </p>
                    </div>
                    <AlertCircle className="h-8 w-8 text-red-200" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-3xl font-bold">
                        {stats.duplicateRows}
                      </p>
                      <p className="text-yellow-100 font-medium">Duplicates</p>
                    </div>
                    <AlertCircle className="h-8 w-8 text-yellow-200" />
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-center space-x-4 mb-8">
              <button
                onClick={() => setShowDataView(!showDataView)}
                className="flex items-center bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl transform hover:scale-105"
              >
                <Eye className="h-5 w-5 mr-2" />
                <span className="font-medium">
                  {showDataView ? "Hide" : "View"} Data Table
                </span>
              </button>

              <button
                onClick={downloadExcelFile}
                className="flex items-center bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl transform hover:scale-105"
              >
                <Download className="h-5 w-5 mr-2" />
                <span className="font-medium">Download CSV</span>
              </button>
            </div>

            {/* Data Table */}
            {showDataView && (
              <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
                {/* Table Header with Search */}
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <h2 className="text-2xl font-bold text-gray-900">
                      Fund Holdings Data
                    </h2>

                    <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                      {/* Search */}
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <input
                          type="text"
                          placeholder="Search data..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Table Container with Sticky Headers */}
                <div className="overflow-x-auto max-h-180">
                  <table className="min-w-full">
                    <thead className="bg-gray-900 text-white sticky top-0 z-11">
                      <tr>
                        <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider sticky left-0 bg-gray-900 z-20 min-w-20">
                          Row No
                        </th>
                        {columns.map((column) => (
                          <th
                            key={column}
                            className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider min-w-40"
                          >
                            <div className="flex items-center space-x-2">
                              {getColumnIcon(column)}
                              <span>{column.replace(/_/g, " ")}</span>
                            </div>
                          </th>
                        ))}
                        <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider sticky right-0 bg-gray-900 z-20 min-w-24">
                          Status
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider sticky right-0 bg-gray-900 z-20 min-w-32">
                          Duplicate
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider sticky right-0 bg-gray-900 z-20 min-w-60">
                          Reason
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredData.map((row, index) => (
                        <tr
                          key={index}
                          className={`hover:bg-gray-50 transition-colors ${
                            row.is_correct === "False"
                              ? "bg-red-50"
                              : row.duplicate_in_dataset === "True"
                              ? "bg-yellow-50"
                              : "bg-white"
                          }`}
                        >
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 sticky left-0 bg-inherit z-10 min-w-20">
                            {index + 1}
                          </td>
                          {columns.map((column) => (
                            <td
                              key={column}
                              className="px-6 py-4 text-sm text-gray-900"
                            >
                              <div className="flex items-center space-x-2">
                                <span className="text-gray-900">
                                  {row[column] || "-"}
                                </span>
                              </div>
                            </td>
                          ))}
                          <td className="px-6 py-4 whitespace-nowrap sticky right-0 bg-inherit z-10 min-w-32">
                            {getStatusBadge(row)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap sticky right-0 bg-inherit z-10 min-w-32">
                            {getDuplicateStatus(row)}
                          </td>
                          <td className="px-6 py-4 sticky right-0 bg-inherit z-10 min-w-80">
                            <div className="text-xm text-gray-700 bg-gray-100 rounded px-2 py-1">
                              {row.reason || "No reason provided"}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Table Footer */}
                <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>
                      Showing {filteredData.length} of {stats.totalRows} records
                    </span>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span>Invalid: {stats.invalidRows}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <span>Duplicates: {stats.duplicateRows}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>Valid: {stats.validRows}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ExcelDataViewer;
