import json
import os
import pandas as pd
from config.llm_config import get_llm
from config.custom_log import get_logger
from datetime import datetime


class FileValidationAgent:
    def __init__(self, prompt_path=r"ContextGuardrail\file_validation_prompts.txt"):
        self.llm = get_llm()
        self.logger = get_logger("FileValidationAgent", "logs/file_validation.log")
        self.prompt_template = self._load_prompt_template(prompt_path)

    def _load_prompt_template(self, prompt_path):
        try:
            with open(prompt_path, "r", encoding="utf-8") as file:
                return file.read()
        except Exception as e:
            self.logger.error(f"Failed to load prompt template: {e}")
            raise

    def run(self, file_path: str, rule_result: str):
        result = {"success": True, "message": ""}

        file_name = os.path.basename(file_path)
        ext = os.path.splitext(file_path)[1].lower()

        self.logger.info(f"[FileAgent] File received: {file_name}")
        self.logger.info(f"[FileAgent] Detected file extension: {ext}")

        try:
            self.logger.info("[FileAgent] [STEP] Reading Excel file...")
            df = self._read_excel_with_engine(file_path, ext)
            self.logger.info("[FileAgent] Excel file loaded.")
            preview = json.loads(df.head(5).to_json(orient="records", date_format="iso"))
            actual_columns = df.columns.str.strip().str.upper().tolist()
            self.logger.info(f"[FileAgent] DataFrame shape: {df.shape}")
            self.logger.info(f"[FileAgent] Extracted Columns: {actual_columns}")
        except Exception as e:
            self.logger.error(f"[FileAgent] Could not read Excel file: {e}")
            return {"success": False, "message": str(e)}

        try:
            self.logger.info("[FileAgent] Loading and parsing schema definition...")
            with open(rule_result, "r", encoding="utf-8") as f:
                schema_model = json.load(f)  
            self.logger.info("[FileAgent] Schema loaded as dictionary.")
        except Exception as e:
            self.logger.error(f"[FileAgent] Failed to load/parse schema: {e}")
            return {"success": False, "message": "Invalid schema format"}

        prompt = self._build_prompt(schema_model, actual_columns, preview)
        self.logger.debug(f"[FileAgent] LLM Prompt:\n{prompt}")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        prompt_log_path = f"logs/llm_prompt_{timestamp}.txt"
        response_log_path = f"logs/llm_output_{timestamp}.txt"

        try:
            with open(prompt_log_path, "w", encoding="utf-8") as pf:
                pf.write(prompt)
            self.logger.info(f"[FileAgent] LLM prompt saved to: {prompt_log_path}")

            self.logger.info("[FileAgent] Invoking LLM for schema validation...")
            response = self.llm(prompt)
            self.logger.debug(f"[FileAgent] LLM Response:\n{response}")

            with open(response_log_path, "w", encoding="utf-8") as rf:
                rf.write(response)
            self.logger.info(f"[FileAgent] LLM response saved to: {response_log_path}")

            if "INVALID" in response.upper():
                result["success"] = False

            result["message"] = response
            return result

        except Exception as e:
            self.logger.error(f"[FileAgent] LLM validation failed: {e}")
            return {"success": False, "message": str(e)}

    def _read_excel_with_engine(self, file_path, ext):
        if ext == ".xls":
            return pd.read_excel(file_path, engine='xlrd')
        elif ext == ".xlsx":
            return pd.read_excel(file_path, engine='openpyxl')
        else:
            raise ValueError(f"Unsupported file extension: {ext}")

    def _build_prompt(self, schema_model: dict, actual_columns: list, preview: list) -> str:
        return self.prompt_template.format(
            expected_schema=json.dumps(schema_model, indent=2),
            actual_columns=json.dumps(actual_columns, indent=2),
            sample_data=json.dumps(preview, indent=2)
        )
