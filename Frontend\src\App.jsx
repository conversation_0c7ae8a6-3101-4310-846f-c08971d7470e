import React, { useState, useCallback } from "react";
import {
  Upload,
  FileText,
  AlertCircle,
  CheckCircle,
  Download,
  Eye,
  X,
  Filter,
  Search,
} from "lucide-react";
import * as XLSX from "xlsx";

const PropertyValidationSystem = () => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [originalData, setOriginalData] = useState([]);
  const [validatedData, setValidatedData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [columnTemplate, setColumnTemplate] = useState(null);
  const [validationRules, setValidationRules] = useState([]);
  const [processingStatus, setProcessingStatus] = useState("idle");
  const [filterType, setFilterType] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [validationSummary, setValidationSummary] = useState({
    totalRows: 0,
    validRows: 0,
    criticalIssues: 0,
    warnings: 0,
  });
  const [showDataView, setShowDataView] = useState(false);

  const columns =
    validatedData.length > 0
      ? Object.keys(validatedData[0]).filter(
          (key) =>
            ![
              "rowIndex",
              "hasError",
              "hasWarning",
              "errorMessages",
              "warningMessages",
            ].includes(key)
        )
      : [];

  // Default validation rules
  const defaultValidationRules = [
    {
      field: "Property ID",
      type: "required",
      pattern: /^PROP-\d{4}$/,
      message:
        "Property ID must follow format PROP-XXXX where XXXX is a 4-digit number",
    },
    {
      field: "Lease Start Date",
      type: "date",
      format: "YYYY-MM-DD",
      message: "Date must be in YYYY-MM-DD format",
    },
    {
      field: "Monthly Rent",
      type: "currency",
      pattern: /^\$[\d,]+\.\d{2}$/,
      message: "Currency must be in format $X,XXX.XX",
    },
    {
      field: "Tenant Name",
      type: "required",
      message: "Tenant Name is mandatory for all lease records",
    },
    {
      field: "Square Footage",
      type: "numeric",
      message: "Square footage should be numeric only",
    },
    {
      field: "Lease End Date",
      type: "date",
      format: "YYYY-MM-DD",
      message: "Date must be in YYYY-MM-DD format",
    },
  ];

  // Generate column template from uploaded data
  const generateColumnTemplate = (data) => {
    if (!data || data.length === 0) return null;

    const headers = Object.keys(data[0]);
    const template = {
      tableName: "property_management_data",
      totalColumns: headers.length,
      columns: headers.map((header, index) => ({
        columnIndex: index,
        columnName: header,
        dataType: inferDataType(data, header),
        isRequired: isRequiredField(header),
        validationRules: getValidationRulesForColumn(header),
      })),
    };

    return template;
  };

  const inferDataType = (data, columnName) => {
    const sample = data.slice(0, 10);
    const values = sample
      .map((row) => row[columnName])
      .filter((val) => val !== null && val !== undefined && val !== "");

    if (values.length === 0) return "string";

    if (values.every((val) => !isNaN(val) && !isNaN(parseFloat(val))))
      return "number";
    if (values.every((val) => /^\d{4}-\d{2}-\d{2}$/.test(val))) return "date";
    if (values.every((val) => /^\$[\d,]+\.\d{2}$/.test(val))) return "currency";

    return "string";
  };

  const isRequiredField = (columnName) => {
    const requiredFields = ["Property ID", "Tenant Name", "Lease Start Date"];
    return requiredFields.includes(columnName);
  };

  const getValidationRulesForColumn = (columnName) => {
    return defaultValidationRules.filter((rule) => rule.field === columnName);
  };

  // Validate data against rules
  const validateData = (data, rules) => {
    let criticalCount = 0;
    let warningCount = 0;

    const validatedData = data.map((row, index) => {
      const errors = [];
      const warnings = [];

      rules.forEach((rule) => {
        const value = row[rule.field];
        const validationResult = validateField(value, rule);

        if (!validationResult.isValid) {
          if (rule.type === "required") {
            errors.push(validationResult.message);
            criticalCount++;
          } else {
            warnings.push(validationResult.message);
            warningCount++;
          }
        }
      });

      return {
        ...row,
        rowIndex: index + 1,
        hasError: errors.length > 0,
        hasWarning: warnings.length > 0,
        errorMessages: errors.join("; "),
        warningMessages: warnings.join("; "),
      };
    });

    setValidationSummary({
      totalRows: data.length,
      validRows: data.length - criticalCount,
      criticalIssues: criticalCount,
      warnings: warningCount,
    });

    return validatedData;
  };

  const validateField = (value, rule) => {
    if (rule.type === "required") {
      if (!value || value.toString().trim() === "" || value === "[EMPTY]") {
        return { isValid: false, message: rule.message };
      }
    }

    if (value && rule.pattern && !rule.pattern.test(value.toString())) {
      return { isValid: false, message: rule.message };
    }

    return { isValid: true, message: "" };
  };

  // Filter and search functionality
  const applyFilters = (data) => {
    let filtered = data;

    // Apply status filter
    if (filterType === "errors") {
      filtered = filtered.filter((row) => row.hasError);
    } else if (filterType === "warnings") {
      filtered = filtered.filter((row) => row.hasWarning);
    } else if (filterType === "valid") {
      filtered = filtered.filter((row) => !row.hasError && !row.hasWarning);
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter((row) =>
        Object.values(row).some(
          (value) =>
            value &&
            value.toString().toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    return filtered;
  };

  React.useEffect(() => {
    if (validatedData.length > 0) {
      setFilteredData(applyFilters(validatedData));
    }
  }, [validatedData, filterType, searchTerm]);

  const handleFileUpload = useCallback((event) => {
    const file = event.target.files[0];
    if (!file) return;

    setUploadedFile(file);
    setProcessingStatus("processing");

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          defval: "[EMPTY]",
        });

        setOriginalData(jsonData);

        // Generate column template
        const template = generateColumnTemplate(jsonData);
        setColumnTemplate(template);
        setValidationRules(defaultValidationRules);

        // Validate data
        const validated = validateData(jsonData, defaultValidationRules);
        setValidatedData(validated);

        setProcessingStatus("completed");
      } catch (error) {
        console.error("Error processing file:", error);
        setProcessingStatus("error");
      }
    };

    reader.readAsArrayBuffer(file);
  }, []);

  const downloadValidatedFile = () => {
    if (validatedData.length === 0) return;

    const ws = XLSX.utils.json_to_sheet(validatedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Validated_Data");
    XLSX.writeFile(wb, `validated_${uploadedFile.name}`);
  };

  const getColumnIcon = (columnName) => {
    const iconClass = "w-2 h-2 rounded-full";
    switch (columnName.toLowerCase()) {
      case "property id":
        return <div className={`${iconClass} bg-blue-500`}></div>;
      case "lease start date":
      case "lease end date":
        return <div className={`${iconClass} bg-purple-500`}></div>;
      case "monthly rent":
        return <div className={`${iconClass} bg-green-500`}></div>;
      case "tenant name":
        return <div className={`${iconClass} bg-orange-500`}></div>;
      case "square footage":
        return <div className={`${iconClass} bg-pink-500`}></div>;
      default:
        return <div className={`${iconClass} bg-gray-500`}></div>;
    }
  };

  const isFieldValid = (row, column) => {
    const value = row[column];
    const rule = defaultValidationRules.find((r) => r.field === column);

    if (!rule) return true;

    const validationResult = validateField(value, rule);
    return validationResult.isValid;
  };

  const getStatusBadge = (row) => {
    if (row.hasError) {
      return (
        <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <AlertCircle className="w-3 h-3 mr-1" />
          Critical
        </div>
      );
    } else if (row.hasWarning) {
      return (
        <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          <AlertCircle className="w-3 h-3 mr-1" />
          Warning
        </div>
      );
    }
    return (
      <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Valid
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7x2 mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">File Agent </h1>
          <p className="text-lg text-gray-600">
            Professional Excel validation with real-time error detection
          </p>
        </div>

        {/* Upload Section */}
        
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-200 w-1/2 ml-auto mr-auto">
            <div className="flex items-center space-x-4 justify-between">
              <div className="flex items-center space-x-4">
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  Upload Excel File
                </h2>
              </div>
              <div className="flex items-center space-x-4">
                {uploadedFile && (
                  <div className="flex items-center bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                    <FileText className="h-5 w-5 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-blue-900">
                      {uploadedFile.name}
                    </span>
                  </div>
                )}
                <label htmlFor="file-upload" className="cursor-pointer">
                  <div className="flex items-center bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl transform hover:scale-105">
                    <Upload className="h-5 w-5 mr-2" />
                    <span className="font-medium">Choose File</span>
                  </div>
                  <input
                    id="file-upload"
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </label>
              </div>
            </div>
          </div>
       
        {/* Processing Status */}
        {processingStatus === "processing" && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
              <span className="text-lg font-medium text-blue-800">
                Processing file and validating data...
              </span>
            </div>
          </div>
        )}

        {/* Validation Summary */}
        {processingStatus === "completed" && (
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Validation Summary
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-3xl font-bold">
                      {validationSummary.totalRows}
                    </p>
                    <p className="text-blue-100 font-medium">Total Rows</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-3xl font-bold">
                      {validationSummary.validRows}
                    </p>
                    <p className="text-green-100 font-medium">Valid Entries</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-3xl font-bold">
                      {validationSummary.criticalIssues}
                    </p>
                    <p className="text-red-100 font-medium">Critical Issues</p>
                  </div>
                  <X className="h-8 w-8 text-red-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-3xl font-bold">
                      {validationSummary.warnings}
                    </p>
                    <p className="text-yellow-100 font-medium">Warnings</p>
                  </div>
                  <AlertCircle className="h-8 w-8 text-yellow-200" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {processingStatus === "completed" && (
          <div className="flex justify-center space-x-4 mb-8">
            <button
              onClick={() => setShowDataView(!showDataView)}
              className="flex items-center bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl transform hover:scale-105"
            >
              <Eye className="h-5 w-5 mr-2" />
              <span className="font-medium">
                {showDataView ? "Hide" : "View"} Data Results
              </span>
            </button>

            <button
              onClick={downloadValidatedFile}
              className="flex items-center bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl transform hover:scale-105"
            >
              <Download className="h-5 w-5 mr-2" />
              <span className="font-medium">Download Results</span>
            </button>
          </div>
        )}

        {/* Data Table */}
        {showDataView && processingStatus === "completed" && (
          <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
            {/* Table Header with Filters */}
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <h2 className="text-2xl font-bold text-gray-900">
                  Data Validation Results
                </h2>

                <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="Search data..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  {/* Filter */}
                  <div className="flex items-center space-x-2">
                    <Filter className="h-4 w-4 text-gray-500" />
                    <select
                      value={filterType}
                      onChange={(e) => setFilterType(e.target.value)}
                      className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">
                        All Rows ({validationSummary.totalRows})
                      </option>
                      <option value="errors">
                        Errors Only ({validationSummary.criticalIssues})
                      </option>
                      <option value="warnings">
                        Warnings Only ({validationSummary.warnings})
                      </option>
                      <option value="valid">
                        Valid Only ({validationSummary.validRows})
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Table Container with Sticky Headers */}
            <div className="overflow-x-auto max-h-180">
              <table className="min-w-full">
                <thead className="bg-gray-900 text-white sticky top-0 z-11">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider sticky left-0 bg-gray-900 z-20 min-w-10">
                      Row
                    </th>
                    {columns.map((column) => (
                      <th
                        key={column}
                        className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider min-w-40"
                      >
                        <div className="flex items-center space-x-2">
                          {getColumnIcon(column)}
                          <span>{column}</span>
                        </div>
                      </th>
                    ))}
                    <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider sticky right-0 bg-gray-900 z-20 min-w-32">
                      Status
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold uppercase tracking-wider sticky right-0 bg-gray-900 z-20 min-w-40">
                      Validation Issues
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredData.map((row, index) => (
                    <tr
                      key={index}
                      className={`hover:bg-gray-50 transition-colors ${
                        row.hasError
                          ? "bg-red-50"
                          : row.hasWarning
                          ? "bg-yellow-50"
                          : "bg-white"
                      }`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 sticky left-0 bg-inherit z-10 min-w-20">
                        #{row.rowIndex}
                      </td>
                      {columns.map((column) => (
                        <td
                          key={column}
                          className="px-6 py-4 text-sm text-gray-900"
                        >
                          {row[column] === "[EMPTY]" ? (
                            <span className="text-gray-400 italic">Empty</span>
                          ) : (
                            <div className="flex items-center space-x-2">
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  isFieldValid(row, column)
                                    ? "bg-green-500"
                                    : "bg-red-500"
                                }`}
                              ></div>
                              <span
                                className={
                                  !isFieldValid(row, column)
                                    ? "text-red-600 font-medium"
                                    : "text-gray-900"
                                }
                              >
                                {row[column]}
                              </span>
                            </div>
                          )}
                        </td>
                      ))}
                      <td className="px-6 py-4 whitespace-nowrap sticky right-0 bg-inherit z-10 min-w-32">
                        {getStatusBadge(row)}
                      </td>
                      <td className="px-6 py-4 sticky right-0 bg-inherit z-10 min-w-80">
                        <div className="space-y-1">
                          {row.hasError && (
                            <div className="text-xs text-red-700 bg-red-100 rounded px-2 py-1">
                              <strong>Error:</strong> {row.errorMessages}
                            </div>
                          )}
                          {row.hasWarning && (
                            <div className="text-xs text-yellow-700 bg-yellow-100 rounded px-2 py-1">
                              <strong>Warning:</strong> {row.warningMessages}
                            </div>
                          )}
                          {!row.hasError && !row.hasWarning && (
                            <div className="text-xs text-green-700 bg-green-100 rounded px-2 py-1">
                              <strong>Valid:</strong> All checks passed
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Table Footer */}
            <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  Showing {filteredData.length} of {validationSummary.totalRows}{" "}
                  rows
                </span>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span>Errors: {validationSummary.criticalIssues}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span>Warnings: {validationSummary.warnings}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>Valid: {validationSummary.validRows}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PropertyValidationSystem;
