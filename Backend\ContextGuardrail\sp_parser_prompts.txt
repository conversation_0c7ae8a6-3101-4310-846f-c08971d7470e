
# Placeholder for ContextGuardrail/sp_parser_prompts.txt

You are a store procedure rule extraction expert.

A column template "CBRE_GenAI_Validation_System\data\inputs\FundHolding_Metadata_Columns 1.json" and a SQL stored procedure "CBRE_GenAI_Validation_System\data\inputs\stored_procedures.json"are provided to you.

Your job is to extract **schema validation rules** and **data validation rules** from the procedure, using the context of the column template.

If column names are not explicitly mentioned in the procedure, try to **infer them**. If they truly cannot be determined, use an **empty list** for the `"columns"` field.

You are also allowed to define abstract rules like `"Column count in uploaded file should match template"` or `"Missing required columns"`.

⚠️ Do NOT return any natural language or explanation. Just return a valid JSON response in the structure below.

----------------------
📁 COLUMN TEMPLATE
Required Columns:
{required_columns}

Data Types:
{data_types}

----------------------
🧾 STORED PROCEDURE
{procedure_text}

----------------------
✅ OUTPUT FORMAT (JSON ONLY):
{{
  "schema_validation": [
    {{
      "rule": "description of the schema validation rule",
      "source_procedure": "{procedure_name}",
      "columns": [...],
      "type": "column_presence_check / column_count_check / datatype_check"
    }}
  ],
  "data_validation": [
    {{
      "rule": "description of the data validation rule",
      "source_procedure": "{procedure_name}",
      "columns": [...],
      "type": "null_check / value_range / format_check / duplicate_check / business_rule"
    }}
  ]
}}


